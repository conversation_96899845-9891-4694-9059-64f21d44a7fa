# -*- coding: utf-8 -*-
"""
批量重命名对话框 - 支持多种重命名模式
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QListWidget, QListWidgetItem,
                            QLineEdit, QMessageBox, QProgressBar, QTextEdit,
                            QGroupBox, QSpinBox, QFormLayout)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from utils.config_manager import ConfigManager
from utils.file_operations import FileOperations
from models.database import DatabaseManager
from pathlib import Path
import os


class BatchRenameWorker(QThread):
    """批量重命名工作线程"""
    
    progress_updated = pyqtSignal(int)  # 进度更新信号
    file_renamed = pyqtSignal(str, str, bool)  # 文件重命名完成信号：原名，新名，是否成功
    rename_completed = pyqtSignal(int, int)  # 重命名完成信号：成功数量，总数量
    
    def __init__(self, files: list, rename_pattern: dict, file_operations: FileOperations):
        super().__init__()
        self.files = files
        self.rename_pattern = rename_pattern
        self.file_operations = file_operations
        
    def run(self):
        """执行重命名任务"""
        success_count = 0
        total_count = len(self.files)
        
        for i, file_info in enumerate(self.files):
            try:
                old_name = file_info['name']
                new_name = self.generate_new_name(file_info, i)
                
                if new_name and new_name != old_name:
                    # 执行重命名
                    success = self.file_operations.rename_file(file_info['id'], new_name)
                    if success:
                        success_count += 1
                    
                    self.file_renamed.emit(old_name, new_name, success)
                else:
                    self.file_renamed.emit(old_name, old_name, True)  # 名称未改变
                    success_count += 1
                
            except Exception as e:
                print(f"重命名文件失败: {file_info['name']}, 错误: {e}")
                self.file_renamed.emit(file_info['name'], "", False)
            
            # 更新进度
            progress = int((i + 1) / total_count * 100)
            self.progress_updated.emit(progress)
        
        # 发送重命名完成信号
        self.rename_completed.emit(success_count, total_count)
    
    def generate_new_name(self, file_info: dict, index: int) -> str:
        """生成新文件名"""
        # 序号重命名
        return self.generate_sequence_name(file_info, index)
    
    def generate_sequence_name(self, file_info: dict, index: int) -> str:
        """生成序号名称"""
        pattern = self.rename_pattern
        prefix = pattern.get('prefix', 'File')
        start_number = pattern.get('start_number', 1)
        digits = pattern.get('digits', 3)
        
        # 生成序号
        number = start_number + index
        number_str = str(number).zfill(digits)
        
        # 获取原文件扩展名
        original_ext = Path(file_info['name']).suffix
        
        return f"{prefix}_{number_str}{original_ext}"
    



class BatchRenameDialog(QDialog):
    """批量重命名对话框"""
    
    files_renamed = pyqtSignal(list)  # 重命名完成信号
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, 
                 selected_files: list, current_category: str = "", parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.file_operations = FileOperations(config_manager, db_manager)
        self.selected_files = selected_files
        self.current_category = current_category
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("批量重命名")
        self.setFixedSize(600, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("批量重命名文件")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 文件列表
        file_group = QGroupBox(f"要重命名的文件 ({len(self.selected_files)} 个)")
        file_layout = QVBoxLayout(file_group)
        
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(120)
        
        for file_info in self.selected_files:
            item = QListWidgetItem(file_info['name'])
            item.setToolTip(file_info.get('path', ''))
            self.file_list.addItem(item)
        
        file_layout.addWidget(self.file_list)
        layout.addWidget(file_group)
        
        # 重命名设置
        rename_group = QGroupBox("重命名设置")
        rename_layout = QFormLayout(rename_group)

        # 基础名称
        self.base_name_edit = QLineEdit()
        self.base_name_edit.setPlaceholderText("输入基础名称...")
        rename_layout.addRow("基础名称:", self.base_name_edit)

        # 起始编号
        self.start_number_spin = QSpinBox()
        self.start_number_spin.setRange(1, 9999)
        self.start_number_spin.setValue(1)
        rename_layout.addRow("起始编号:", self.start_number_spin)

        # 编号位数
        self.digits_spin = QSpinBox()
        self.digits_spin.setRange(1, 5)
        self.digits_spin.setValue(2)
        rename_layout.addRow("编号位数:", self.digits_spin)

        layout.addWidget(rename_group)
        
        # 预览区域
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_list = QListWidget()
        self.preview_list.setMaximumHeight(150)
        preview_layout.addWidget(self.preview_list)
        
        self.preview_btn = QPushButton("预览重命名结果")
        preview_layout.addWidget(self.preview_btn)
        
        layout.addWidget(preview_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(80)
        self.log_text.setVisible(False)
        layout.addWidget(self.log_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.rename_btn = QPushButton("开始重命名")
        self.cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(self.rename_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def setup_connections(self):
        """设置信号连接"""
        self.preview_btn.clicked.connect(self.preview_rename)
        self.rename_btn.clicked.connect(self.start_rename)
        self.cancel_btn.clicked.connect(self.reject)
    

    
    def get_rename_pattern(self) -> dict:
        """获取当前的重命名模式"""
        return {
            'type': 'sequence',
            'prefix': self.base_name_edit.text().strip(),
            'start_number': self.start_number_spin.value(),
            'digits': self.digits_spin.value()
        }
    
    def preview_rename(self):
        """预览重命名结果"""
        pattern = self.get_rename_pattern()
        
        # 验证模式
        if not self.validate_pattern(pattern):
            return
        
        # 生成预览
        self.preview_list.clear()
        
        worker = BatchRenameWorker(self.selected_files, pattern, self.file_operations)
        
        for i, file_info in enumerate(self.selected_files):
            try:
                new_name = worker.generate_new_name(file_info, i)
                old_name = file_info['name']
                
                if new_name != old_name:
                    preview_text = f"{old_name} → {new_name}"
                else:
                    preview_text = f"{old_name} (无变化)"
                
                item = QListWidgetItem(preview_text)
                self.preview_list.addItem(item)
                
            except Exception as e:
                item = QListWidgetItem(f"{file_info['name']} → 错误: {str(e)}")
                self.preview_list.addItem(item)
    
    def validate_pattern(self, pattern: dict) -> bool:
        """验证重命名模式"""
        if not pattern.get('prefix'):
            QMessageBox.warning(self, "警告", "基础名称不能为空")
            return False
        return True
    
    def start_rename(self):
        """开始重命名"""
        pattern = self.get_rename_pattern()
        
        if not self.validate_pattern(pattern):
            return
        
        # 确认重命名
        reply = QMessageBox.question(
            self, "确认重命名",
            f"确定要重命名 {len(self.selected_files)} 个文件吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # 显示进度条和日志
        self.progress_bar.setVisible(True)
        self.log_text.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        
        # 禁用按钮
        self.rename_btn.setEnabled(False)
        self.preview_btn.setEnabled(False)
        
        # 开始重命名
        self.rename_worker = BatchRenameWorker(self.selected_files, pattern, self.file_operations)
        self.rename_worker.progress_updated.connect(self.progress_bar.setValue)
        self.rename_worker.file_renamed.connect(self.on_file_renamed)
        self.rename_worker.rename_completed.connect(self.on_rename_completed)
        self.rename_worker.start()
    
    def on_file_renamed(self, old_name: str, new_name: str, success: bool):
        """单个文件重命名完成"""
        if success:
            if new_name != old_name:
                self.log_text.append(f"{old_name} → {new_name}")
            else:
                self.log_text.append(f"{old_name} (无变化)")
        else:
            self.log_text.append(f"{old_name} → 失败")
    
    def on_rename_completed(self, success_count: int, total_count: int):
        """重命名完成"""
        self.progress_bar.setValue(100)
        
        # 显示结果
        if success_count == total_count:
            QMessageBox.information(
                self, "重命名完成", 
                f"成功重命名 {success_count} 个文件"
            )
        else:
            QMessageBox.warning(
                self, "重命名完成", 
                f"重命名完成：成功 {success_count} 个，失败 {total_count - success_count} 个"
            )
        
        # 发送完成信号
        self.files_renamed.emit(self.selected_files)
        
        # 重新启用按钮
        self.rename_btn.setEnabled(True)
        self.preview_btn.setEnabled(True)
        
        # 可以选择关闭对话框
        self.accept()
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #888888;
        }
        
        QLineEdit, QSpinBox {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QComboBox {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QListWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QTextEdit {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QTabWidget::pane {
            border: 1px solid #5a5a5a;
            background-color: #3c3c3c;
        }
        
        QTabBar::tab {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QTabBar::tab:selected {
            background-color: #5a5a5a;
        }
        
        QProgressBar {
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 2px;
        }
        
        QCheckBox {
            color: #ffffff;
        }
        """
        self.setStyleSheet(style)
