# -*- coding: utf-8 -*-
"""
批量重命名对话框 - 支持多种重命名模式
"""

from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QComboBox, QListWidget, QListWidgetItem,
                            QLineEdit, QMessageBox, QProgressBar, QTextEdit,
                            QGroupBox, QCheckBox, QRadioButton, QButtonGroup,
                            QSpinBox, QFormLayout, QTabWidget, QWidget)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont

from utils.config_manager import ConfigManager
from utils.file_operations import FileOperations
from models.database import DatabaseManager
from pathlib import Path
import os
import re
from datetime import datetime


class BatchRenameWorker(QThread):
    """批量重命名工作线程"""
    
    progress_updated = pyqtSignal(int)  # 进度更新信号
    file_renamed = pyqtSignal(str, str, bool)  # 文件重命名完成信号：原名，新名，是否成功
    rename_completed = pyqtSignal(int, int)  # 重命名完成信号：成功数量，总数量
    
    def __init__(self, files: list, rename_pattern: dict, file_operations: FileOperations):
        super().__init__()
        self.files = files
        self.rename_pattern = rename_pattern
        self.file_operations = file_operations
        
    def run(self):
        """执行重命名任务"""
        success_count = 0
        total_count = len(self.files)
        
        for i, file_info in enumerate(self.files):
            try:
                old_name = file_info['name']
                new_name = self.generate_new_name(file_info, i)
                
                if new_name and new_name != old_name:
                    # 执行重命名
                    success = self.file_operations.rename_file(file_info['id'], new_name)
                    if success:
                        success_count += 1
                    
                    self.file_renamed.emit(old_name, new_name, success)
                else:
                    self.file_renamed.emit(old_name, old_name, True)  # 名称未改变
                    success_count += 1
                
            except Exception as e:
                print(f"重命名文件失败: {file_info['name']}, 错误: {e}")
                self.file_renamed.emit(file_info['name'], "", False)
            
            # 更新进度
            progress = int((i + 1) / total_count * 100)
            self.progress_updated.emit(progress)
        
        # 发送重命名完成信号
        self.rename_completed.emit(success_count, total_count)
    
    def generate_new_name(self, file_info: dict, index: int) -> str:
        """生成新文件名"""
        pattern_type = self.rename_pattern['type']
        
        if pattern_type == 'character':
            # 人物特殊格式：名称-表情-动作-序号
            return self.generate_character_name(file_info, index)
        elif pattern_type == 'time':
            # 按时间重命名
            return self.generate_time_name(file_info, index)
        elif pattern_type == 'sequence':
            # 序号重命名
            return self.generate_sequence_name(file_info, index)
        elif pattern_type == 'custom':
            # 自定义模式
            return self.generate_custom_name(file_info, index)
        
        return file_info['name']
    
    def generate_character_name(self, file_info: dict, index: int) -> str:
        """生成人物格式名称：名称-表情-动作-序号"""
        pattern = self.rename_pattern
        parts = []

        # 名称（必填）
        if pattern.get('name'):
            parts.append(pattern['name'])

        # 表情（可选）
        if pattern.get('expression') and pattern.get('use_expression', False):
            parts.append(pattern['expression'])

        # 动作（可选）
        if pattern.get('action') and pattern.get('use_action', False):
            parts.append(pattern['action'])

        # 添加序号（从1开始）
        if pattern.get('use_sequence', True):
            sequence_num = pattern.get('start_number', 1) + index
            digits = pattern.get('digits', 2)
            parts.append(str(sequence_num).zfill(digits))

        # 获取原文件扩展名
        original_ext = Path(file_info['name']).suffix

        if parts:
            new_name = '-'.join(parts) + original_ext
        else:
            new_name = file_info['name']

        return new_name
    
    def generate_time_name(self, file_info: dict, index: int) -> str:
        """按时间生成名称"""
        pattern = self.rename_pattern
        time_format = pattern.get('time_format', '%Y%m%d_%H%M%S')
        
        # 使用文件的创建时间或当前时间
        if pattern.get('use_file_time', True):
            # 尝试从文件信息获取时间
            creation_time = file_info.get('creation_time')
            if creation_time:
                try:
                    if isinstance(creation_time, str):
                        dt = datetime.fromisoformat(creation_time.replace('Z', '+00:00'))
                    else:
                        dt = creation_time
                except:
                    dt = datetime.now()
            else:
                dt = datetime.now()
        else:
            dt = datetime.now()
        
        # 格式化时间
        time_str = dt.strftime(time_format)
        
        # 添加序号（如果需要）
        if pattern.get('add_sequence', True):
            time_str += f"_{index + 1:03d}"
        
        # 获取原文件扩展名
        original_ext = Path(file_info['name']).suffix
        
        return time_str + original_ext
    
    def generate_sequence_name(self, file_info: dict, index: int) -> str:
        """生成序号名称"""
        pattern = self.rename_pattern
        prefix = pattern.get('prefix', 'File')
        start_number = pattern.get('start_number', 1)
        digits = pattern.get('digits', 3)
        
        # 生成序号
        number = start_number + index
        number_str = str(number).zfill(digits)
        
        # 获取原文件扩展名
        original_ext = Path(file_info['name']).suffix
        
        return f"{prefix}_{number_str}{original_ext}"
    
    def generate_custom_name(self, file_info: dict, index: int) -> str:
        """生成自定义名称"""
        pattern = self.rename_pattern
        template = pattern.get('template', '{name}')
        
        # 替换变量
        variables = {
            'name': Path(file_info['name']).stem,
            'ext': Path(file_info['name']).suffix,
            'index': str(index + 1),
            'index_3': str(index + 1).zfill(3),
            'date': datetime.now().strftime('%Y%m%d'),
            'time': datetime.now().strftime('%H%M%S'),
            'category': file_info.get('category', ''),
        }
        
        new_name = template
        for var, value in variables.items():
            new_name = new_name.replace(f'{{{var}}}', value)
        
        # 确保有扩展名
        if not new_name.endswith(Path(file_info['name']).suffix):
            new_name += Path(file_info['name']).suffix
        
        return new_name


class BatchRenameDialog(QDialog):
    """批量重命名对话框"""
    
    files_renamed = pyqtSignal(list)  # 重命名完成信号
    
    def __init__(self, config_manager: ConfigManager, db_manager: DatabaseManager, 
                 selected_files: list, current_category: str = "", parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.db_manager = db_manager
        self.file_operations = FileOperations(config_manager, db_manager)
        self.selected_files = selected_files
        self.current_category = current_category
        
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("批量重命名")
        self.setFixedSize(600, 700)
        
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("批量重命名文件")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # 文件列表
        file_group = QGroupBox(f"要重命名的文件 ({len(self.selected_files)} 个)")
        file_layout = QVBoxLayout(file_group)
        
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(120)
        
        for file_info in self.selected_files:
            item = QListWidgetItem(file_info['name'])
            item.setToolTip(file_info.get('path', ''))
            self.file_list.addItem(item)
        
        file_layout.addWidget(self.file_list)
        layout.addWidget(file_group)
        
        # 重命名模式选择
        self.tab_widget = QTabWidget()
        
        # 人物模式（仅在人物分类时显示）
        if self.current_category == "人物":
            self.character_tab = self.create_character_tab()
            self.tab_widget.addTab(self.character_tab, "人物模式")
        
        # 时间模式
        self.time_tab = self.create_time_tab()
        self.tab_widget.addTab(self.time_tab, "时间模式")
        
        # 序号模式
        self.sequence_tab = self.create_sequence_tab()
        self.tab_widget.addTab(self.sequence_tab, "序号模式")
        
        # 自定义模式
        self.custom_tab = self.create_custom_tab()
        self.tab_widget.addTab(self.custom_tab, "自定义模式")
        
        layout.addWidget(self.tab_widget)
        
        # 预览区域
        preview_group = QGroupBox("预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_list = QListWidget()
        self.preview_list.setMaximumHeight(150)
        preview_layout.addWidget(self.preview_list)
        
        self.preview_btn = QPushButton("预览重命名结果")
        preview_layout.addWidget(self.preview_btn)
        
        layout.addWidget(preview_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(80)
        self.log_text.setVisible(False)
        layout.addWidget(self.log_text)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.rename_btn = QPushButton("开始重命名")
        self.cancel_btn = QPushButton("取消")
        
        button_layout.addWidget(self.rename_btn)
        button_layout.addWidget(self.cancel_btn)
        layout.addLayout(button_layout)
        
        # 应用样式
        self.apply_styles()
    
    def create_character_tab(self) -> QWidget:
        """创建人物模式标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 名称（必填）
        self.char_name_edit = QLineEdit()
        self.char_name_edit.setPlaceholderText("例如：男1")
        layout.addRow("名称*:", self.char_name_edit)
        
        # 表情（可选）
        self.char_expression_checkbox = QCheckBox("使用表情")
        self.char_expression_edit = QLineEdit()
        self.char_expression_edit.setPlaceholderText("例如：害羞")
        self.char_expression_edit.setEnabled(False)
        
        expression_layout = QHBoxLayout()
        expression_layout.addWidget(self.char_expression_checkbox)
        expression_layout.addWidget(self.char_expression_edit)
        layout.addRow("表情:", expression_layout)
        
        # 动作（可选）
        self.char_action_checkbox = QCheckBox("使用动作")
        self.char_action_edit = QLineEdit()
        self.char_action_edit.setPlaceholderText("例如：跑步")
        self.char_action_edit.setEnabled(False)
        
        action_layout = QHBoxLayout()
        action_layout.addWidget(self.char_action_checkbox)
        action_layout.addWidget(self.char_action_edit)
        layout.addRow("动作:", action_layout)

        # 序号设置
        self.char_sequence_checkbox = QCheckBox("添加序号")
        self.char_sequence_checkbox.setChecked(True)
        layout.addRow("", self.char_sequence_checkbox)

        # 序号起始数字
        self.char_start_number_spin = QSpinBox()
        self.char_start_number_spin.setRange(1, 9999)
        self.char_start_number_spin.setValue(1)
        layout.addRow("起始序号:", self.char_start_number_spin)

        # 序号位数
        self.char_digits_spin = QSpinBox()
        self.char_digits_spin.setRange(1, 5)
        self.char_digits_spin.setValue(2)
        layout.addRow("序号位数:", self.char_digits_spin)

        # 连接信号
        self.char_expression_checkbox.toggled.connect(self.char_expression_edit.setEnabled)
        self.char_action_checkbox.toggled.connect(self.char_action_edit.setEnabled)

        return widget
    
    def create_time_tab(self) -> QWidget:
        """创建时间模式标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 时间格式
        self.time_format_combo = QComboBox()
        self.time_format_combo.addItems([
            "%Y%m%d_%H%M%S",
            "%Y-%m-%d_%H-%M-%S", 
            "%Y%m%d",
            "%H%M%S",
            "自定义"
        ])
        layout.addRow("时间格式:", self.time_format_combo)
        
        # 自定义时间格式
        self.custom_time_edit = QLineEdit()
        self.custom_time_edit.setPlaceholderText("例如：%Y年%m月%d日")
        self.custom_time_edit.setEnabled(False)
        layout.addRow("自定义格式:", self.custom_time_edit)
        
        # 使用文件时间
        self.use_file_time_checkbox = QCheckBox("使用文件创建时间")
        self.use_file_time_checkbox.setChecked(True)
        layout.addRow("", self.use_file_time_checkbox)
        
        # 添加序号
        self.add_sequence_checkbox = QCheckBox("添加序号")
        self.add_sequence_checkbox.setChecked(True)
        layout.addRow("", self.add_sequence_checkbox)
        
        # 连接信号
        self.time_format_combo.currentTextChanged.connect(
            lambda text: self.custom_time_edit.setEnabled(text == "自定义")
        )
        
        return widget
    
    def create_sequence_tab(self) -> QWidget:
        """创建序号模式标签页"""
        widget = QWidget()
        layout = QFormLayout(widget)
        
        # 前缀
        self.seq_prefix_edit = QLineEdit()
        self.seq_prefix_edit.setText("File")
        layout.addRow("前缀:", self.seq_prefix_edit)
        
        # 起始数字
        self.seq_start_spin = QSpinBox()
        self.seq_start_spin.setRange(0, 9999)
        self.seq_start_spin.setValue(1)
        layout.addRow("起始数字:", self.seq_start_spin)
        
        # 数字位数
        self.seq_digits_spin = QSpinBox()
        self.seq_digits_spin.setRange(1, 10)
        self.seq_digits_spin.setValue(3)
        layout.addRow("数字位数:", self.seq_digits_spin)
        
        return widget
    
    def create_custom_tab(self) -> QWidget:
        """创建自定义模式标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模板说明
        help_label = QLabel(
            "可用变量：\n"
            "{name} - 原文件名（不含扩展名）\n"
            "{ext} - 文件扩展名\n"
            "{index} - 序号\n"
            "{index_3} - 3位序号（001, 002...）\n"
            "{date} - 当前日期（YYYYMMDD）\n"
            "{time} - 当前时间（HHMMSS）\n"
            "{category} - 文件分类"
        )
        help_label.setStyleSheet("color: #888888; font-size: 10px;")
        layout.addWidget(help_label)
        
        # 模板输入
        form_layout = QFormLayout()
        self.custom_template_edit = QLineEdit()
        self.custom_template_edit.setText("{name}_{index_3}")
        self.custom_template_edit.setPlaceholderText("例如：{name}_{index_3}")
        form_layout.addRow("重命名模板:", self.custom_template_edit)
        
        layout.addLayout(form_layout)
        
        return widget
    
    def setup_connections(self):
        """设置信号连接"""
        self.preview_btn.clicked.connect(self.preview_rename)
        self.rename_btn.clicked.connect(self.start_rename)
        self.cancel_btn.clicked.connect(self.reject)
    
    def get_rename_pattern(self) -> dict:
        """获取当前的重命名模式"""
        current_tab = self.tab_widget.currentIndex()
        tab_text = self.tab_widget.tabText(current_tab)
        
        if tab_text == "人物模式":
            return {
                'type': 'character',
                'name': self.char_name_edit.text().strip(),
                'use_expression': self.char_expression_checkbox.isChecked(),
                'expression': self.char_expression_edit.text().strip(),
                'use_action': self.char_action_checkbox.isChecked(),
                'action': self.char_action_edit.text().strip(),
                'use_sequence': self.char_sequence_checkbox.isChecked(),
                'start_number': self.char_start_number_spin.value(),
                'digits': self.char_digits_spin.value()
            }
        elif tab_text == "时间模式":
            time_format = self.time_format_combo.currentText()
            if time_format == "自定义":
                time_format = self.custom_time_edit.text().strip()
            
            return {
                'type': 'time',
                'time_format': time_format,
                'use_file_time': self.use_file_time_checkbox.isChecked(),
                'add_sequence': self.add_sequence_checkbox.isChecked()
            }
        elif tab_text == "序号模式":
            return {
                'type': 'sequence',
                'prefix': self.seq_prefix_edit.text().strip(),
                'start_number': self.seq_start_spin.value(),
                'digits': self.seq_digits_spin.value()
            }
        elif tab_text == "自定义模式":
            return {
                'type': 'custom',
                'template': self.custom_template_edit.text().strip()
            }
        
        return {'type': 'none'}
    
    def preview_rename(self):
        """预览重命名结果"""
        pattern = self.get_rename_pattern()
        
        # 验证模式
        if not self.validate_pattern(pattern):
            return
        
        # 生成预览
        self.preview_list.clear()
        
        worker = BatchRenameWorker(self.selected_files, pattern, self.file_operations)
        
        for i, file_info in enumerate(self.selected_files):
            try:
                new_name = worker.generate_new_name(file_info, i)
                old_name = file_info['name']
                
                if new_name != old_name:
                    preview_text = f"{old_name} → {new_name}"
                else:
                    preview_text = f"{old_name} (无变化)"
                
                item = QListWidgetItem(preview_text)
                self.preview_list.addItem(item)
                
            except Exception as e:
                item = QListWidgetItem(f"{file_info['name']} → 错误: {str(e)}")
                self.preview_list.addItem(item)
    
    def validate_pattern(self, pattern: dict) -> bool:
        """验证重命名模式"""
        if pattern['type'] == 'character':
            if not pattern.get('name'):
                QMessageBox.warning(self, "警告", "人物模式下名称不能为空")
                return False
        elif pattern['type'] == 'time':
            if not pattern.get('time_format'):
                QMessageBox.warning(self, "警告", "时间格式不能为空")
                return False
        elif pattern['type'] == 'sequence':
            if not pattern.get('prefix'):
                QMessageBox.warning(self, "警告", "序号模式下前缀不能为空")
                return False
        elif pattern['type'] == 'custom':
            if not pattern.get('template'):
                QMessageBox.warning(self, "警告", "自定义模式下模板不能为空")
                return False
        
        return True
    
    def start_rename(self):
        """开始重命名"""
        pattern = self.get_rename_pattern()
        
        if not self.validate_pattern(pattern):
            return
        
        # 确认重命名
        reply = QMessageBox.question(
            self, "确认重命名",
            f"确定要重命名 {len(self.selected_files)} 个文件吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply != QMessageBox.StandardButton.Yes:
            return
        
        # 显示进度条和日志
        self.progress_bar.setVisible(True)
        self.log_text.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()
        
        # 禁用按钮
        self.rename_btn.setEnabled(False)
        self.preview_btn.setEnabled(False)
        
        # 开始重命名
        self.rename_worker = BatchRenameWorker(self.selected_files, pattern, self.file_operations)
        self.rename_worker.progress_updated.connect(self.progress_bar.setValue)
        self.rename_worker.file_renamed.connect(self.on_file_renamed)
        self.rename_worker.rename_completed.connect(self.on_rename_completed)
        self.rename_worker.start()
    
    def on_file_renamed(self, old_name: str, new_name: str, success: bool):
        """单个文件重命名完成"""
        if success:
            if new_name != old_name:
                self.log_text.append(f"{old_name} → {new_name}")
            else:
                self.log_text.append(f"{old_name} (无变化)")
        else:
            self.log_text.append(f"{old_name} → 失败")
    
    def on_rename_completed(self, success_count: int, total_count: int):
        """重命名完成"""
        self.progress_bar.setValue(100)
        
        # 显示结果
        if success_count == total_count:
            QMessageBox.information(
                self, "重命名完成", 
                f"成功重命名 {success_count} 个文件"
            )
        else:
            QMessageBox.warning(
                self, "重命名完成", 
                f"重命名完成：成功 {success_count} 个，失败 {total_count - success_count} 个"
            )
        
        # 发送完成信号
        self.files_renamed.emit(self.selected_files)
        
        # 重新启用按钮
        self.rename_btn.setEnabled(True)
        self.preview_btn.setEnabled(True)
        
        # 可以选择关闭对话框
        self.accept()
    
    def apply_styles(self):
        """应用样式"""
        style = """
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #5a5a5a;
            border-radius: 5px;
            margin-top: 10px;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        
        QPushButton:pressed {
            background-color: #6a6a6a;
        }
        
        QPushButton:disabled {
            background-color: #3a3a3a;
            color: #888888;
        }
        
        QLineEdit, QSpinBox {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QComboBox {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QListWidget {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QTextEdit {
            background-color: #3c3c3c;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            color: #ffffff;
        }
        
        QTabWidget::pane {
            border: 1px solid #5a5a5a;
            background-color: #3c3c3c;
        }
        
        QTabBar::tab {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            padding: 8px 16px;
            color: #ffffff;
        }
        
        QTabBar::tab:selected {
            background-color: #5a5a5a;
        }
        
        QProgressBar {
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            text-align: center;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 2px;
        }
        
        QCheckBox {
            color: #ffffff;
        }
        """
        self.setStyleSheet(style)
