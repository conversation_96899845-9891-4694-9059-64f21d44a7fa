# -*- coding: utf-8 -*-
"""
主窗口 - 应用程序的主界面
"""

from PyQt6.QtWidgets import (QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
                            QSplitter, QToolBar, QStatusBar, QPushButton,
                            QLineEdit, QLabel, QFrame, QMenu, QComboBox, QMessageBox, QDialog)
from PyQt6.QtCore import Qt, QSize, QTimer
from PyQt6.QtGui import QIcon, QAction, QKeySequence
from pathlib import Path
from typing import Optional

from .category_list import CategoryList
from .file_view import FileView
from .preview_panel import PreviewPanel
from .mini_window import MiniWindow
from .settings_dialog import SettingsDialog
from .import_dialog import ImportDialog
from .export_dialog import ExportDialog
from models.database import DatabaseManager
from utils.config_manager import ConfigManager
from utils.navigation import NavigationManager
from utils.search_sort import SearchSortManager, Sort<PERSON>y, SortOrder


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.db_manager = DatabaseManager(str(config_manager.config_dir / "metadata.db"))
        self.navigation_manager = NavigationManager(config_manager)
        self.search_sort_manager = SearchSortManager(config_manager, self.db_manager)
        self.mini_window = None

        self.init_ui()
        self.setup_shortcuts()
        self.restore_window_state()
        self.setup_navigation()

        # 如果是首次运行，显示欢迎信息
        if self.config_manager.is_first_run():
            self.show_welcome_message()

        # 设置定时清理回收站
        self.setup_auto_cleanup()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简笔画素材管理")
        self.setMinimumSize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧分类列表
        self.category_list = CategoryList(self.config_manager, self.db_manager)
        splitter.addWidget(self.category_list)
        
        # 创建中间文件视图
        self.file_view = FileView(self.config_manager, self.db_manager)
        splitter.addWidget(self.file_view)
        
        # 创建右侧预览面板
        self.preview_panel = PreviewPanel(self.config_manager)
        splitter.addWidget(self.preview_panel)

        # 设置分割器比例
        splitter.setSizes([200, 600, 300])

        # 保存分割器引用以便控制预览面板
        self.main_splitter = splitter
        
        # 连接信号
        self.category_list.category_changed.connect(self.on_category_changed)
        self.file_view.file_selected.connect(self.preview_panel.show_file_preview)
        
        # 创建工具栏和状态栏
        self.create_toolbar()
        self.create_bottom_toolbar()  # 添加底部功能栏
        self.create_statusbar()

        # 应用样式
        self.apply_styles()
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = QToolBar("主工具栏")
        toolbar.setMovable(False)
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        self.addToolBar(toolbar)
        
        # 导航按钮
        self.back_action = QAction("←", self)
        self.back_action.setToolTip("返回上一级")
        self.back_action.setEnabled(False)
        self.back_action.triggered.connect(self.go_back)
        toolbar.addAction(self.back_action)

        self.forward_action = QAction("→", self)
        self.forward_action.setToolTip("前进到下一级")
        self.forward_action.setEnabled(False)
        self.forward_action.triggered.connect(self.go_forward)
        toolbar.addAction(self.forward_action)
        
        toolbar.addSeparator()
        
        # 导入按钮
        import_action = QAction("导入", self)
        import_action.setToolTip("导入文件")
        import_action.triggered.connect(self.import_files)
        toolbar.addAction(import_action)
        
        toolbar.addSeparator()
        
        # 搜索框
        search_label = QLabel("搜索:")
        toolbar.addWidget(search_label)

        # 搜索范围选择
        self.search_scope_combo = QComboBox()
        self.search_scope_combo.addItems(["全局", "当前分类"])
        self.search_scope_combo.setCurrentText("当前分类")
        self.search_scope_combo.setMaximumWidth(100)
        self.search_scope_combo.currentTextChanged.connect(self.on_search_scope_changed)
        toolbar.addWidget(self.search_scope_combo)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入搜索关键词...")
        self.search_edit.setMaximumWidth(200)
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        toolbar.addWidget(self.search_edit)
        
        toolbar.addSeparator()
        
        # 导出按钮
        export_action = QAction("导出", self)
        export_action.setToolTip("导出选中文件")
        export_action.triggered.connect(self.export_files)
        toolbar.addAction(export_action)
        
        # 排序切换栏
        sort_label = QLabel("排序:")
        toolbar.addWidget(sort_label)

        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["按名称", "按类型", "按创建时间", "按后缀"])
        self.sort_combo.setCurrentText("按名称")
        self.sort_combo.setMaximumWidth(100)
        self.sort_combo.currentTextChanged.connect(self.on_sort_changed)
        toolbar.addWidget(self.sort_combo)
        
        # 设置按钮
        settings_action = QAction("设置", self)
        settings_action.setToolTip("应用设置")
        settings_action.triggered.connect(self.show_settings)
        toolbar.addAction(settings_action)
        
        # 小窗模式按钮
        mini_action = QAction("小窗模式", self)
        mini_action.setToolTip("切换到小窗模式")
        mini_action.triggered.connect(self.toggle_mini_window)
        toolbar.addAction(mini_action)

    def create_bottom_toolbar(self):
        """创建底部功能栏"""
        bottom_toolbar = QToolBar("底部功能栏")
        bottom_toolbar.setMovable(False)
        bottom_toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextBesideIcon)
        self.addToolBar(Qt.ToolBarArea.BottomToolBarArea, bottom_toolbar)

        # 全选按钮
        select_all_action = QAction("全选", self)
        select_all_action.setToolTip("全选当前视图中的所有文件")
        select_all_action.triggered.connect(self.select_all)
        bottom_toolbar.addAction(select_all_action)

        bottom_toolbar.addSeparator()

        # 批量重命名按钮
        batch_rename_action = QAction("批量重命名", self)
        batch_rename_action.setToolTip("批量重命名选中的文件")
        batch_rename_action.triggered.connect(self.batch_rename)
        bottom_toolbar.addAction(batch_rename_action)

        # 删除选中按钮
        delete_selected_action = QAction("删除选中", self)
        delete_selected_action.setToolTip("删除选中的文件")
        delete_selected_action.triggered.connect(self.delete_selected)
        bottom_toolbar.addAction(delete_selected_action)

        # 更改颜色按钮
        change_color_action = QAction("更改颜色", self)
        change_color_action.setToolTip("更改选中文件的颜色")
        change_color_action.triggered.connect(self.change_color)
        bottom_toolbar.addAction(change_color_action)

        # 回收站特有的批量还原按钮（初始隐藏）
        self.restore_action = QAction("批量还原", self)
        self.restore_action.setToolTip("还原选中的文件到原位置")
        self.restore_action.triggered.connect(self.restore_selected)
        self.restore_action.setVisible(False)  # 默认隐藏
        bottom_toolbar.addAction(self.restore_action)
    
    def create_statusbar(self):
        """创建状态栏"""
        self.statusbar = QStatusBar()
        self.setStatusBar(self.statusbar)
        
        # 显示就绪状态
        self.statusbar.showMessage("就绪")
    
    def setup_shortcuts(self):
        """设置快捷键"""
        # F2 - 重命名
        rename_shortcut = QKeySequence("F2")
        rename_action = QAction(self)
        rename_action.setShortcut(rename_shortcut)
        rename_action.triggered.connect(self.rename_selected)
        self.addAction(rename_action)
        
        # Ctrl+F - 搜索
        search_shortcut = QKeySequence("Ctrl+F")
        search_action = QAction(self)
        search_action.setShortcut(search_shortcut)
        search_action.triggered.connect(self.focus_search)
        self.addAction(search_action)
        
        # Ctrl+A - 全选
        select_all_shortcut = QKeySequence("Ctrl+A")
        select_all_action = QAction(self)
        select_all_action.setShortcut(select_all_shortcut)
        select_all_action.triggered.connect(self.select_all)
        self.addAction(select_all_action)
        
        # Alt+Space+X - 小窗模式
        mini_shortcut = QKeySequence("Alt+Space, X")
        mini_action = QAction(self)
        mini_action.setShortcut(mini_shortcut)
        mini_action.triggered.connect(self.toggle_mini_window)
        self.addAction(mini_action)

        # Alt+Left - 后退
        back_shortcut = QKeySequence("Alt+Left")
        back_action = QAction(self)
        back_action.setShortcut(back_shortcut)
        back_action.triggered.connect(self.go_back)
        self.addAction(back_action)

        # Alt+Right - 前进
        forward_shortcut = QKeySequence("Alt+Right")
        forward_action = QAction(self)
        forward_action.setShortcut(forward_shortcut)
        forward_action.triggered.connect(self.go_forward)
        self.addAction(forward_action)

        # Backspace - 返回上一级
        up_shortcut = QKeySequence("Backspace")
        up_action = QAction(self)
        up_action.setShortcut(up_shortcut)
        up_action.triggered.connect(self.go_up_level)
        self.addAction(up_action)

        # 1-5 数字键快速切换分类
        categories = ["人物", "场景", "道具", "其他", "回收站"]
        for i, category in enumerate(categories, 1):
            shortcut = QKeySequence(str(i))
            action = QAction(self)
            action.setShortcut(shortcut)
            action.triggered.connect(lambda checked, cat=category: self.switch_to_category(cat))
            self.addAction(action)
    
    def apply_styles(self):
        """应用样式表"""
        style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QToolBar {
            background-color: #3c3c3c;
            border: none;
            spacing: 5px;
            padding: 5px;
        }
        
        QToolBar QToolButton {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px 10px;
            color: #ffffff;
        }
        
        QToolBar QToolButton:hover {
            background-color: #5a5a5a;
        }
        
        QToolBar QToolButton:pressed {
            background-color: #6a6a6a;
        }
        
        QLineEdit {
            background-color: #4a4a4a;
            border: 1px solid #5a5a5a;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        
        QStatusBar {
            background-color: #3c3c3c;
            color: #ffffff;
            border-top: 1px solid #5a5a5a;
        }
        """
        self.setStyleSheet(style)
    
    def restore_window_state(self):
        """恢复窗口状态"""
        pos = self.config_manager.settings["window_position"]
        self.setGeometry(pos["x"], pos["y"], pos["width"], pos["height"])
    
    def show_welcome_message(self):
        """显示欢迎信息"""
        self.statusbar.showMessage("欢迎使用简笔画素材管理软件！")
        self.config_manager.set_first_run_complete()
    
    # 槽函数
    def import_files(self):
        """导入文件"""
        current_category = self.category_list.get_current_category()
        import_dialog = ImportDialog(self.config_manager, self.db_manager, current_category, self)

        if import_dialog.exec() == QDialog.DialogCode.Accepted:
            # 导入完成后刷新文件视图
            self.file_view.refresh_current_view()
            self.statusbar.showMessage("文件导入完成")
    
    def export_files(self):
        """导出文件"""
        selected_files = self.file_view.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要导出的文件")
            return

        export_dialog = ExportDialog(self.config_manager, self.db_manager, selected_files, self)

        if export_dialog.exec() == QDialog.DialogCode.Accepted:
            self.statusbar.showMessage("文件导出完成")
    

    
    def show_settings(self):
        """显示设置对话框"""
        settings_dialog = SettingsDialog(self.config_manager, self)
        settings_dialog.exec()
    
    def toggle_mini_window(self):
        """切换小窗模式"""
        if self.mini_window is None:
            self.mini_window = MiniWindow(self.config_manager, self.db_manager)
            # 连接小窗关闭信号，当小窗关闭时重新显示主窗口
            self.mini_window.closed.connect(self.on_mini_window_closed)

        if self.mini_window.isVisible():
            self.mini_window.hide()
            self.show()
        else:
            self.hide()
            self.mini_window.show()

    def on_mini_window_closed(self):
        """小窗关闭时的处理"""
        self.show()  # 重新显示主窗口
    
    def on_search_text_changed(self, text):
        """搜索文本改变"""
        if text.strip():
            # 获取搜索范围
            search_scope = self.search_scope_combo.currentText()

            # 根据搜索范围确定分类
            if search_scope == "全局":
                search_category = None  # 全局搜索
            else:
                search_category = self.category_list.get_current_category()  # 当前分类搜索

            # 执行搜索（支持文件名和类型的模糊搜索）
            search_results = self.search_sort_manager.search_files(text, search_category or "")
            self.file_view.display_search_results(search_results)
        else:
            self.file_view.refresh_current_view()

    def on_search_scope_changed(self, search_scope):
        """搜索范围改变"""
        # 如果有搜索文本，重新搜索
        text = self.search_edit.text()
        if text.strip():
            self.on_search_text_changed(text)

    def on_sort_changed(self, sort_text):
        """排序方式改变"""
        # 映射排序文本到排序类型
        sort_mapping = {
            "按名称": SortBy.NAME,
            "按类型": SortBy.TYPE,
            "按创建时间": SortBy.MODIFIED_TIME,
            "按后缀": SortBy.TYPE  # 按后缀实际上也是按类型
        }

        sort_by = sort_mapping.get(sort_text, SortBy.NAME)
        self.search_sort_manager.set_sort_preferences(sort_by, SortOrder.ASCENDING)

        # 刷新当前视图以应用新的排序
        self.file_view.refresh_current_view()

    def _merge_search_results(self, list1, list2):
        """合并搜索结果并去重"""
        seen = set()
        merged = []

        for item in list1 + list2:
            item_id = item.get('id') or item.get('path', '')
            if item_id not in seen:
                seen.add(item_id)
                merged.append(item)

        return merged
    
    def rename_selected(self):
        """重命名选中项"""
        self.file_view.rename_selected()
    
    def focus_search(self):
        """聚焦搜索框"""
        self.search_edit.setFocus()
        self.search_edit.selectAll()
    
    def select_all(self):
        """全选"""
        self.file_view.select_all()

    def setup_navigation(self):
        """设置导航功能"""
        self.update_navigation_buttons()

    def on_category_changed(self, category: str):
        """分类改变事件"""
        # 添加到导航历史
        self.navigation_manager.add_location(category)

        # 切换文件视图
        self.file_view.set_category(category)

        # 更新导航按钮状态
        self.update_navigation_buttons()

        # 更新底部工具栏按钮显示状态
        self.update_bottom_toolbar_visibility(category)

    def go_back(self):
        """后退导航"""
        location = self.navigation_manager.go_back()
        if location:
            category = location.get("category", "")
            subfolder = location.get("subfolder", "")

            # 更新分类选择（不触发信号）
            self.category_list.set_current_category(category, emit_signal=False)

            # 切换文件视图
            self.file_view.set_category(category, subfolder)

            # 更新导航按钮状态
            self.update_navigation_buttons()

    def go_forward(self):
        """前进导航"""
        location = self.navigation_manager.go_forward()
        if location:
            category = location.get("category", "")
            subfolder = location.get("subfolder", "")

            # 更新分类选择（不触发信号）
            self.category_list.set_current_category(category, emit_signal=False)

            # 切换文件视图
            self.file_view.set_category(category, subfolder)

            # 更新导航按钮状态
            self.update_navigation_buttons()

    def update_navigation_buttons(self):
        """更新导航按钮状态"""
        self.back_action.setEnabled(self.navigation_manager.can_go_back())
        self.forward_action.setEnabled(self.navigation_manager.can_go_forward())

    def set_sort_by(self, sort_by: SortBy):
        """设置排序方式"""
        _, current_sort_order = self.search_sort_manager.get_sort_preferences()
        self.search_sort_manager.set_sort_preferences(sort_by, current_sort_order)
        self.file_view.refresh_current_view()
        self.statusbar.showMessage(f"排序方式已更改为: {sort_by.value}")

    def set_sort_order(self, sort_order: SortOrder):
        """设置排序顺序"""
        current_sort_by, _ = self.search_sort_manager.get_sort_preferences()
        self.search_sort_manager.set_sort_preferences(current_sort_by, sort_order)
        self.file_view.refresh_current_view()
        self.statusbar.showMessage(f"排序顺序已更改为: {'升序' if sort_order == SortOrder.ASCENDING else '降序'}")

    def go_up_level(self):
        """返回上一级目录"""
        current_subfolder = self.file_view.current_subfolder
        if current_subfolder:
            # 如果在子文件夹中，返回上一级
            parent_folder = str(Path(current_subfolder).parent)
            if parent_folder == ".":
                parent_folder = ""

            self.file_view.set_category(self.file_view.current_category, parent_folder)

            # 添加到导航历史
            self.navigation_manager.add_location(
                self.file_view.current_category,
                parent_folder
            )
            self.update_navigation_buttons()

    def closeEvent(self, event):
        """关闭事件"""
        # 保存窗口状态
        geometry = self.geometry()
        self.config_manager.settings["window_position"] = {
            "x": geometry.x(),
            "y": geometry.y(),
            "width": geometry.width(),
            "height": geometry.height()
        }
        self.config_manager.save_settings()

        # 关闭小窗口
        if self.mini_window:
            self.mini_window.close()

        event.accept()

    # 新增的方法
    def switch_to_category(self, category: str):
        """快速切换到指定分类"""
        self.category_list.set_current_category(category, emit_signal=True)

    def update_bottom_toolbar_visibility(self, category: str):
        """根据当前分类更新底部工具栏按钮的显示状态"""
        # 回收站显示批量还原按钮，其他分类隐藏
        is_recycle_bin = (category == "回收站")
        self.restore_action.setVisible(is_recycle_bin)

    def batch_rename(self):
        """批量重命名"""
        selected_files = self.file_view.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要重命名的文件")
            return

        # 这里应该打开批量重命名对话框
        self.file_view.batch_rename()

    def delete_selected(self):
        """删除选中的文件"""
        selected_files = self.file_view.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要删除的文件")
            return

        self.file_view.delete_selected()

    def change_color(self):
        """更改选中文件的颜色"""
        selected_files = self.file_view.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要更改颜色的文件")
            return

        self.file_view.change_color()

    def restore_selected(self):
        """还原选中的文件（仅回收站）"""
        if self.category_list.get_current_category() != "回收站":
            return

        selected_files = self.file_view.get_selected_files()
        if not selected_files:
            QMessageBox.information(self, "提示", "请先选择要还原的文件")
            return

        self.file_view.restore_selected()

    def setup_auto_cleanup(self):
        """设置自动清理回收站"""
        # 创建定时器，每天检查一次
        self.cleanup_timer = QTimer()
        self.cleanup_timer.timeout.connect(self.auto_cleanup_recycle_bin)

        # 设置为24小时（86400000毫秒）
        self.cleanup_timer.start(24 * 60 * 60 * 1000)

        # 启动时也执行一次清理
        self.auto_cleanup_recycle_bin()

    def auto_cleanup_recycle_bin(self):
        """自动清理回收站"""
        try:
            # 检查是否有超过30天的文件
            old_files_count = self.db_manager.get_old_deleted_files_count(30)

            if old_files_count > 0:
                # 执行清理
                deleted_count = self.db_manager.cleanup_old_deleted_files(30)

                if deleted_count > 0:
                    print(f"自动清理回收站：删除了 {deleted_count} 个超过30天的文件")

                    # 如果当前在回收站页面，刷新视图
                    if self.category_list.get_current_category() == "回收站":
                        self.file_view.refresh_current_view()

        except Exception as e:
            print(f"自动清理回收站失败: {e}")
